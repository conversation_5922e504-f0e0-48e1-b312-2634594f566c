<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Technoloway Admin</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/modern-homepage.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/admin.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Enhanced Modern Sidebar -->
        <div class="modern-sidebar" id="sidebar-wrapper">
            <!-- Logo Section -->
            <div class="sidebar-header">
                <a class="sidebar-brand" asp-area="Admin" asp-controller="Home" asp-action="Index">
                    <div class="brand-icon">
                        <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" class="brand-logo" />
                    </div>
                    <div class="brand-text">
                        <span class="brand-name">Technoloway</span>
                        <span class="brand-subtitle">Admin Panel</span>
                    </div>
                </a>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a class="nav-item" asp-area="Admin" asp-controller="Home" asp-action="Index" data-page="dashboard">
                        <div class="nav-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <span class="nav-text">Dashboard</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Content Management</div>
                    <a class="nav-item" asp-area="Admin" asp-controller="HeroSections" asp-action="Index" data-page="hero-sections">
                        <div class="nav-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <span class="nav-text">Hero Sections</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="AboutPages" asp-action="Index" data-page="about-pages">
                        <div class="nav-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <span class="nav-text">About Pages</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Services" asp-action="Index" data-page="services">
                        <div class="nav-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <span class="nav-text">Services</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Projects" asp-action="Index" data-page="projects">
                        <div class="nav-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <span class="nav-text">Projects</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Technologies" asp-action="Index" data-page="technologies">
                        <div class="nav-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <span class="nav-text">Technologies</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="TeamMembers" asp-action="Index" data-page="team">
                        <div class="nav-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <span class="nav-text">Team Members</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Blog" asp-action="Index" data-page="blog">
                        <div class="nav-icon">
                            <i class="fas fa-blog"></i>
                        </div>
                        <span class="nav-text">Blog</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="LegalPages" asp-action="Index" data-page="legal-pages">
                        <div class="nav-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <span class="nav-text">Legal Pages</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Business</div>
                    <a class="nav-item" asp-area="Admin" asp-controller="Jobs" asp-action="Index" data-page="jobs">
                        <div class="nav-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <span class="nav-text">Jobs</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Clients" asp-action="Index" data-page="clients">
                        <div class="nav-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <span class="nav-text">Clients</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Invoices" asp-action="Index" data-page="invoices">
                        <div class="nav-icon">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <span class="nav-text">Invoices</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Testimonials" asp-action="Index" data-page="testimonials">
                        <div class="nav-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <span class="nav-text">Testimonials</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="ContactForms" asp-action="Index" data-page="contact">
                        <div class="nav-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <span class="nav-text">Contact Forms</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <a class="nav-item" asp-area="Admin" asp-controller="Settings" asp-action="Index" data-page="settings">
                        <div class="nav-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="nav-text">Settings</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Users" asp-action="Index" data-page="users">
                        <div class="nav-icon">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <span class="nav-text">Users & Roles</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="sidebar-user">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-name">Admin User</div>
                        <div class="user-role">Administrator</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Page Content -->
        <div id="page-content-wrapper">
            <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                <div class="container-fluid">
                    <button class="btn btn-dark" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
                            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarSupportedContent">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>View Site
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button"
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle me-1"></i>Account
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <li><a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">Profile</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form class="form-inline" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                                            <button type="submit" class="dropdown-item">Logout</button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <div class="container-fluid p-4">
                @RenderBody()
            </div>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/admin.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
