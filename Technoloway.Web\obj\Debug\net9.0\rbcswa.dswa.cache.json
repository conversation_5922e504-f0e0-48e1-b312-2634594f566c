{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["MzxVtf5OdcY8X0yp9XY8bmkyHaRsatnCnyGZncTlHrs=", "+bN4FsSt8nAJFtVeciThORW0V7sHdEybss9ILLQidCU=", "jBOLx9kLujDGSqKmJNFg85PhckSOQwzwuR7rrzUU5R8=", "6IP6kQoknsA+Do/VnfqkxMyii5fy/V43tvdfbLWrCXI=", "WB9ofBd3pMqW7VHldqzsf4IsO6H/jXCdrs+70wsCnto=", "nsVuRVIPeZhhEGPUhRvdaBs0dFpfCEBCCepYwPExwVU=", "12bmWIGJKUhmDwS9fwEHGc0IeJb1gbjugDsCnlOoIA0=", "els86YOxWXiNRq3JJu4jOwi7xzy/H2iS6UXbFk7Hgqo=", "EE+fjFLpEYYe2Uc1q3YOpMENr84QD9MsmVlnYPrIHsM=", "a08Mz4VNfhK23MQ6n0M/ocQ5n3Y6fcXSfeJjtjco61g=", "RKh8b+F8A7aBHfz/woNvylD09yhmrCp24Ei+Wi21cy8=", "bSZyY5c2XGekDK6At1l1cY9hMwVnt3g6ONV+4jkmKow=", "18otNlnQy7vKblK/xQxLU9HmAdQRv+uav1qpmv1Q52g=", "5w36V3/YShAQNs1Zq9nTWikIHWIGeqcruBYDzLU90nk=", "zCS5vljUKiLYhuWUGaK5+AFiX6XX7sPhYaNRrILJRwQ=", "gXdavJqklst/Fan+jD0x/fdbyZwl+aiT98CmZoXt+UE=", "Bg/S6404EybhSWfdmwRU/1kqSvga9Mr4efLauN1wpyU=", "RLHYxxDQ1+hDGHOaTgUcsEMHFiDpDu+cPRTg0slEWI8=", "Z+qe2JOsPE+mQ3ReudAryZtgoD+Eg8AW0iFxJ3vEKwA=", "TgDK2qtrFqgB/dYIroVAvnCZvXrXaLOm7uf403aFiRw=", "RGG7X9AYLTDb+8Wpms2f6KrWmcRMEckfYVBhlZ0nDm8=", "OT43ehZhRDaXTQQdkbYOkoihyz0jhMuqZlpE7KBOPrA=", "z5p+Fjl/lDONWgMbXJfEce6nqmR1LrLYpYIBynMDZ4Y=", "bJp0QzzM5+0ADVC5XdEprNiQPhXySqj/wjllhOYlKtI=", "NrXLit+EGlsdd2H9Uj45Ayu8M+syI39JbH1P4XJD6eM=", "auBW3WNU/S+zzvZj3enjIT/ifyJK5FQwrQAvDBy97UE=", "IZg82273NRv40uqX6HTUlFDL9c3Pjug87gEUeclJ01A=", "Q8gTCc41nXDmeVvy366wtk6qLP7aBkeaU1/0UgsCRMQ=", "nC/WFDXx+36rdm7gnZ9sXK+7vo2GoC3N6CDOKX7c3yI=", "ul35a9D4MXire5xuL3VmFAYAiWfwpKNhLc3Ge//PGwg=", "1cAD4FRh+afDAgUpZyCS9UfL6V1c5pHnKAbk3S/7J78=", "P57OLEVL8nuvUl/9a2F9ckIpY3PbDuk/TvRRFGPUUhc=", "oQJe7D2O1BBD59FVk+HqxWAMiY79xCzserVY3FHaQJ8=", "RHjsaY2+tV4lnW79jmJkaktkNjcskBSShv0npGjMEnE=", "T+co3+v7d1hXBpWlCkfT3/WT5Y34+E1NqX3shcDz4Lc=", "1NOl1astOYcqK5R/hhvAze3aXCjdcUlt2vs3ajLaYmA=", "9ATwFEwk8ysOZIYLRj0xvfl+qVSUEugGSDxVJFki4xM=", "NvrCsNdXvQhQ8dh34JxIf78UR2o44mZKQ5Z1IUylZg8=", "3j+CUCptxaE/C2eegOAjpizlZmjd5LOJcTGg57Pl8mw=", "3TiMqANkhWuqTnpiNJz2+tSbqGyH4YuA8rCQ+kioo6k=", "8uXGixvJ8VlQY6S8+drIC5UcEv+H6WZqGaOTHllOGDw=", "TRgkEXR4sXxlYXL5J0TevQhVeYh3rEkASZFOLQxHLgM=", "aqALOOD/d1XyqnaDhpuRyhknlhNpq7GGclaEC+FwSxY=", "9oJS113ldTPPCA53a2hFJQtixSJsfKTQhxgx4ly2kAs=", "DFnHsQjQIdtJAFwaRrPu1YWc/MKHdBqR6bxrJZinlQM=", "H/P7OOKntdukomoT12O5mVDTTlQNey7AZgy1XKx2eCE=", "m31sMckzE5gfJwE4CQGziOxNCwyeLll1tIsiNurBams=", "8luETe5hfvRCer7s+u3f+/rK7+Oxb3D4yu0yZIzTHGY=", "b0xhaVJrLijx4V5kz4bIZhcYMtRvFhR2vAED8OdPenM=", "W16qQZf1zTkBS7ug+g5UxbFXyZ26ARv7gWdvV8AVHZU=", "lbr7YmNBRpHHxiDdw1JCQRlq83cLZgJVthrOqIXnYYE=", "95Ix5xAKaQUVCxFnhixQVHD47mwoZ2twEp7INtpqb4I=", "zU/dGFBlNcvFk5qbnlNrLtA/oDji1+c/rd73MJLjy/w=", "YgU5eYFCE54ubkx0KYGoksvmYejq/u2R443lCWnC42o=", "cN5IKO7aII4B+3lex9U6yAQGG4MSxPES92IQlvmsn1E=", "BZ+58pDxWgOj/59DYHHS6x7F52wKrkbRf3/rcS/ateY=", "ztA/wjnVqhZ7uw7dwZ3TqbvI/kAnHikgx7Ylc9H62sw=", "ghJBwhT0SSCYJQJpZ5Cq8PFy/XGiVJnpwLoDI9fNP68=", "3YdXFCmKMMCuKOdrEdo4ptvnbY4/73dyOJG9vNxqgEw=", "JKLRzsTBSUz+bUy0D+RUeTEq9LbpsmWhObvvAkGldhU=", "ssxji61bbfNV/53PBec4TXQnU7433onqix8U4rA7/sY=", "id2HW3Vkb2+2SPeTcDTZaMlb4PatAJcfyhmkOIhWBOA=", "C9z2N0d0U422ixAMGAC+e7CLW4TpryBXTBOuBqwShpM=", "KPlsNIqCe9Ya6xSZNQzYHU7yEjrnz5Ewvj2zAXrApZI=", "AcbVExNb/smbauQhZtYgNylIpSKpwpdOPKZoonuOW1U=", "G1RYOw9hYYj7XSf8AUxDFMVaIScAl9BRmRAOyOr0c5k=", "dFiD1Amb9kz+sZ2O7oQpwa+WG0dE47RxtzRrdQ5PX6g=", "0rQ8LnpKh5gy9InAJwK1olgTaHMMUId4c147DT/Au18=", "ZnUt2RD4VIBHq+NHosyq9ua+C8mVwhy3KoFn8ZL4KmQ=", "0XChipx8UadACIlcJBixqp+Pw6GrtZn6RDoSULc6Uxc=", "u3xkZKC+ptZrvuHunL55s+4ETiHCMWSWXNZ/T1clPhA=", "5UZ+aNPuIknZZCGiQeiqjVMxYyw/eRc9uIOu2qnOulg=", "UeTXOcdNMBD6rduEVfDUMd7F4h6LRC0BT8+CZR7Tl0U=", "Oul48G6GLVEfuzbOan+gZeSN8lXx9XVp9k506rVdYAg=", "vE+MEf5yLKgtXCyzuV/8GEsQMD69Ey8HhszyjN38lS8=", "xQVYSg4ELkzmSyBlT8SYQk7PdbojyfOJnLv79qt7BwQ=", "d7v0ap/A4XzjPZZ5W98jCTEAUo/8XB98ybu5e/wFL5o=", "hsPgZdurPdJD4Fw7rpdjy88sBcuRTujwqjXntFn8CtY=", "D+JfMlzPz3/IUTRvZUjH+m1ZMPWkpe0c77Iofj7RRBY=", "uy1wAd3wdcLuQcOPd27Wx6FjUMeQYJJC5CTkFi4E0AU=", "eyey+rWOUBo+clZdAQf/hNo7ZRPWnWKnDsekLLtXYZc=", "aXsy2txNQSn6KcY+6cbgTOZrCLqnY70lLXVXjXn54u4=", "/MlwSSm4q3dvQldOF9x2ZuNPefuqm9B+TRZj9OYhnMI=", "emCiqI5R90cgCECc1G1wBLQEygjN8d0OemP4UcumHDs=", "AhUyLq93sV+Fo5JPOaWja+Pnv6hI8G99+DaFW5VwSVU=", "LdjbR07qhx3ZPyhFm/dSVjMG4lfhdUQov8yJIP80428=", "58MTlyRb8DOzDGE0LibE9kuQTigTlXOEx0aeeECUSWA=", "L/Ic/8HSOkLkcKR5RvzrtCnq5XPv3MlXKQ0hpCCovHE=", "iCl99/bSN1ZmEVRrUEbGrGYEOuSqG+4PJs9I28Vqj/8=", "OjdJPhfvlRFtsy4ZC7mKbzOjB/9ayjOcqcfs1MtTRmY=", "CTanv8wv6beze300ElYquZ8sbFkCrC0SFpWjJpGNaiI=", "rpNcsYNLXiQNZ8l3q9bdWKgwpVXtVsJTYgr2FgcUsLY=", "yUhTEoGZZ0t6faRe+6mrnN6wGV/RPt/ynIqmdVunFjA=", "8ezbZG/H00TAmaxJY28fB6/24DAxoTwLH5gSdumj/Pk=", "ixeoNkBG2adlhb87Ur23zCXsvTDwEWw167M19Ej0m1g=", "C+u2n1XqrI8E5btJ5U3FTyR3FmP2GH78SAU5Qokz4cs=", "EJPWZ0zpVLphZKx1ZSDUvOWpCNgos35JoIdWuqUFJOU=", "1M/cU5sM9EAffnF8IUvygpZl5XjKLqegYr5F6TN6x3s=", "n3mbxxCciZ+m4iUEvJ1JLhB+c+cihwlCbkzidMcpVt4=", "2EuG6ndONlu4fz6kHMFnpqjWB8VMCMVOw0zorsnVcBI=", "7GAuDHeZ3E8SKhuSMKAFwdbxqQAy7O1F1xWgVbiYpaE=", "ed//+NIgnZIKHtwD7PjLkNxNaA30+XbTFZ34+M+YnYw=", "aETklFEzUMwkPlk/8CM8HGrDS5F1OQuBya8djrKL0Nw=", "pUsrFCdsnzo28X3aqTnDt4Q4oASFv8oz1HEmoZHO+sg=", "Ll0nh2xzSt7NX2l674c7qQ+j6GUrEfACQn2uJb/KtCM=", "++0frOA6abau1S9ho/VE71nyqK8EwXu5q/W5nepw7A4=", "OzZ3B65jXBFx1RQ3oiB6GJ/3itALuvo1N0OsECDBwGc=", "gpkfSyet5AXjZa/AeWp+0KHBZlTLD0KRFMC4jbcY5Jo=", "ZkiDmDbSctbKuZlxd2OJl/6eLKw0jTwAamBySt8cgw4=", "fmxNI7i8nHRMKhRbWfRCuUK8Kl8CzydyrB+ctAiBV/w=", "aIQjxER330W+3vt1jJ3MqnPoR//UGEOgdQKgvc/nUYU=", "Rjh8IVfIc1hkdM6XRY500yhX6iM2O/0Ouej9OQ+dqhU=", "nd2KNy8bYqgPXXVaZmPTrnrBdOFreCksnwYJ5cCrXQM=", "sNIYYfPqWnq2CKsd86Lf7W/MlwtwJFY/ek/3xdJYqOU=", "CuAt8KIFzYosTBIsLSjN4YaaZCholwWLAi74SaX14qc=", "7dxJO+H6AmZAm+esnY62ccAjiiVb8ssAhfjq9fIOIVo=", "zWHW9KyLscOIUizvRyLQANRjwHPXk8Ee6tY7X2yX9iE=", "Z25C1W0MHTRbhBkog7sUAhB+FNG+sshAZ9k4aPGFhG0=", "BTIk0QfoqP0ooUR6a1sK5OEtXky4mBPkeyn5rRsEsOc=", "EEYxZB8+v4TUIJDkiIWy7TDudjbxcyIJ/hz63RvrOpQ=", "p+Y6jgHcTUZs1uBkxac9XWRs0UF/n4Bb/k5icG+8Dm0=", "TVSe36T9valgrug6dBrQT5cV4PLcs/SR+CitqaxWyQY=", "KfEWNPyZteelOAn2wjk5auzpIoVkXTP0Il+O8tO7GjU=", "ibdB2cua4hzYTJ0HWZVkEx+8DkS8BkoNBRdjpgYVl14=", "ctBphz1nfw4C/ouDWFv0rqNsSBXszLlek5aVYzuWd9Q=", "tPyrYlFNHH8CySkrns7Llzo60utfeZlkFqNlQIv3OVs=", "jr1rJSndtlwB56ux1BIIm3JjVXFdRQijCuwW83O7wqo=", "DnVYv1LpvkJLmXa66wplZuhNN0R4ko5CChJsNR9ZvJw=", "+jsVCGlnH7Udm16MNBB0eST8zjzMqAMV2uUvzAgd5ig=", "bCA/DSL0k2wm83sDuJPr/J3JetCMiDWpctSDZsfrGxw=", "AwAZePXAO2fJFab5N9qERtsL4Q+UELiQhYDh8Pe8Fxw=", "NpmfVo0EmxbshmXkqNWaI6hbnFr/TIewoDe3oC+96QM=", "j1AFaoo2JK+pGx6rKnRBjLlvrPT1Zi+arDB/W+iR6h4=", "2skbOPUmBI/1U9LowRyS9X+zTejn6kri5Qryzxv2kzw=", "oTYwfkkV4AuVaM2g4xlCDtPkC+0201RlS0nZhYD3/E0=", "/q3tPuPqYiytrXLuoHPGJybOiyrMUbvl7LLsd7DllCw=", "xxVUJfH2iCJZerbirUS8qJKi8wf/TNxkOgNy1k+8180=", "jMXgPt3KgbnFQRPbxr64lg6bfGIvSgSt2QDWEiJrNG0=", "PxiZP0KJcU8GPlwIEWvI95I3/O2lopCpQRYKDBgT1WI=", "q3bBZ3nYFhbHCO/geC+WsAPqNEf00U0rmSewcA1Uk9Q=", "DfWCPBYC9bRgowNnzowBilNGoGlquuXG4uVJqLeXhfM="], "CachedAssets": {"MzxVtf5OdcY8X0yp9XY8bmkyHaRsatnCnyGZncTlHrs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-05-30T20:44:24.0122579+00:00"}, "+bN4FsSt8nAJFtVeciThORW0V7sHdEybss9ILLQidCU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-05-30T20:44:24.0187636+00:00"}, "jBOLx9kLujDGSqKmJNFg85PhckSOQwzwuR7rrzUU5R8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-05-30T20:44:24.0207621+00:00"}, "6IP6kQoknsA+Do/VnfqkxMyii5fy/V43tvdfbLWrCXI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-05-30T20:44:24.0242797+00:00"}, "WB9ofBd3pMqW7VHldqzsf4IsO6H/jXCdrs+70wsCnto=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-05-30T20:44:24.0337824+00:00"}, "nsVuRVIPeZhhEGPUhRvdaBs0dFpfCEBCCepYwPExwVU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-05-30T20:44:24.0357886+00:00"}, "12bmWIGJKUhmDwS9fwEHGc0IeJb1gbjugDsCnlOoIA0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-05-30T20:44:24.0407841+00:00"}, "els86YOxWXiNRq3JJu4jOwi7xzy/H2iS6UXbFk7Hgqo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-05-30T20:44:24.0591593+00:00"}, "EE+fjFLpEYYe2Uc1q3YOpMENr84QD9MsmVlnYPrIHsM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-05-30T20:44:24.0937392+00:00"}, "a08Mz4VNfhK23MQ6n0M/ocQ5n3Y6fcXSfeJjtjco61g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-05-30T20:44:24.1092741+00:00"}, "RKh8b+F8A7aBHfz/woNvylD09yhmrCp24Ei+Wi21cy8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-05-30T20:44:24.1167807+00:00"}, "bSZyY5c2XGekDK6At1l1cY9hMwVnt3g6ONV+4jkmKow=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-05-30T20:44:24.1234242+00:00"}, "18otNlnQy7vKblK/xQxLU9HmAdQRv+uav1qpmv1Q52g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-05-30T20:44:24.1314555+00:00"}, "5w36V3/YShAQNs1Zq9nTWikIHWIGeqcruBYDzLU90nk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-05-30T20:44:24.1334546+00:00"}, "zCS5vljUKiLYhuWUGaK5+AFiX6XX7sPhYaNRrILJRwQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-05-30T20:44:24.1359661+00:00"}, "gXdavJqklst/Fan+jD0x/fdbyZwl+aiT98CmZoXt+UE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-05-30T20:44:24.0493094+00:00"}, "Bg/S6404EybhSWfdmwRU/1kqSvga9Mr4efLauN1wpyU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-05-30T20:44:24.0646654+00:00"}, "RLHYxxDQ1+hDGHOaTgUcsEMHFiDpDu+cPRTg0slEWI8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-05-30T20:44:24.0112583+00:00"}, "Z+qe2JOsPE+mQ3ReudAryZtgoD+Eg8AW0iFxJ3vEKwA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-05-30T20:44:24.0177638+00:00"}, "TgDK2qtrFqgB/dYIroVAvnCZvXrXaLOm7uf403aFiRw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-05-30T20:44:24.0217612+00:00"}, "RGG7X9AYLTDb+8Wpms2f6KrWmcRMEckfYVBhlZ0nDm8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-05-30T20:44:24.0282816+00:00"}, "OT43ehZhRDaXTQQdkbYOkoihyz0jhMuqZlpE7KBOPrA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-05-30T20:44:24.0322787+00:00"}, "z5p+Fjl/lDONWgMbXJfEce6nqmR1LrLYpYIBynMDZ4Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-05-30T20:44:24.0377874+00:00"}, "bJp0QzzM5+0ADVC5XdEprNiQPhXySqj/wjllhOYlKtI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-05-30T20:44:24.0417837+00:00"}, "NrXLit+EGlsdd2H9Uj45Ayu8M+syI39JbH1P4XJD6eM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-05-30T20:44:24.0473104+00:00"}, "auBW3WNU/S+zzvZj3enjIT/ifyJK5FQwrQAvDBy97UE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-05-30T20:44:24.0561607+00:00"}, "IZg82273NRv40uqX6HTUlFDL9c3Pjug87gEUeclJ01A=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-05-30T20:44:24.0591593+00:00"}, "Q8gTCc41nXDmeVvy366wtk6qLP7aBkeaU1/0UgsCRMQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-05-30T20:44:24.0636611+00:00"}, "nC/WFDXx+36rdm7gnZ9sXK+7vo2GoC3N6CDOKX7c3yI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-05-30T20:44:24.1092741+00:00"}, "ul35a9D4MXire5xuL3VmFAYAiWfwpKNhLc3Ge//PGwg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-05-30T20:44:24.1379633+00:00"}, "1cAD4FRh+afDAgUpZyCS9UfL6V1c5pHnKAbk3S/7J78=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-05-30T20:44:24.1549928+00:00"}, "P57OLEVL8nuvUl/9a2F9ckIpY3PbDuk/TvRRFGPUUhc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-05-30T20:44:24.1685208+00:00"}, "oQJe7D2O1BBD59FVk+HqxWAMiY79xCzserVY3FHaQJ8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-05-30T20:44:24.1112735+00:00"}, "RHjsaY2+tV4lnW79jmJkaktkNjcskBSShv0npGjMEnE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-05-30T20:44:24.1494899+00:00"}, "T+co3+v7d1hXBpWlCkfT3/WT5Y34+E1NqX3shcDz4Lc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-05-30T20:44:24.0407841+00:00"}, "1NOl1astOYcqK5R/hhvAze3aXCjdcUlt2vs3ajLaYmA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-05-30T20:44:24.0463119+00:00"}, "9ATwFEwk8ysOZIYLRj0xvfl+qVSUEugGSDxVJFki4xM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-05-30T20:44:24.0636611+00:00"}, "NvrCsNdXvQhQ8dh34JxIf78UR2o44mZKQ5Z1IUylZg8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-05-30T20:44:24.0656638+00:00"}, "3j+CUCptxaE/C2eegOAjpizlZmjd5LOJcTGg57Pl8mw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-05-30T20:44:24.0751886+00:00"}, "3TiMqANkhWuqTnpiNJz2+tSbqGyH4YuA8rCQ+kioo6k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-05-30T20:44:24.0781906+00:00"}, "8uXGixvJ8VlQY6S8+drIC5UcEv+H6WZqGaOTHllOGDw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-05-30T20:44:24.0977443+00:00"}, "TRgkEXR4sXxlYXL5J0TevQhVeYh3rEkASZFOLQxHLgM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-05-30T20:44:24.1082745+00:00"}, "aqALOOD/d1XyqnaDhpuRyhknlhNpq7GGclaEC+FwSxY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-05-30T20:44:24.1209201+00:00"}, "9oJS113ldTPPCA53a2hFJQtixSJsfKTQhxgx4ly2kAs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-05-30T20:44:24.1314555+00:00"}, "DFnHsQjQIdtJAFwaRrPu1YWc/MKHdBqR6bxrJZinlQM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-05-30T20:44:24.1484894+00:00"}, "H/P7OOKntdukomoT12O5mVDTTlQNey7AZgy1XKx2eCE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-05-30T20:44:24.1514895+00:00"}, "m31sMckzE5gfJwE4CQGziOxNCwyeLll1tIsiNurBams=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-05-30T20:44:24.1599927+00:00"}, "8luETe5hfvRCer7s+u3f+/rK7+Oxb3D4yu0yZIzTHGY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-05-30T20:44:24.1645192+00:00"}, "b0xhaVJrLijx4V5kz4bIZhcYMtRvFhR2vAED8OdPenM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-05-30T20:44:24.1665213+00:00"}, "W16qQZf1zTkBS7ug+g5UxbFXyZ26ARv7gWdvV8AVHZU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-05-30T20:44:24.1359661+00:00"}, "lbr7YmNBRpHHxiDdw1JCQRlq83cLZgJVthrOqIXnYYE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-05-30T20:44:24.1379633+00:00"}, "95Ix5xAKaQUVCxFnhixQVHD47mwoZ2twEp7INtpqb4I=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-05-30T20:44:24.013257+00:00"}, "zU/dGFBlNcvFk5qbnlNrLtA/oDji1+c/rd73MJLjy/w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-05-30T20:44:24.0187636+00:00"}, "YgU5eYFCE54ubkx0KYGoksvmYejq/u2R443lCWnC42o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-05-30T20:44:24.0217612+00:00"}, "cN5IKO7aII4B+3lex9U6yAQGG4MSxPES92IQlvmsn1E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-05-30T20:44:24.0262815+00:00"}, "BZ+58pDxWgOj/59DYHHS6x7F52wKrkbRf3/rcS/ateY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-05-30T20:44:24.0367867+00:00"}, "ztA/wjnVqhZ7uw7dwZ3TqbvI/kAnHikgx7Ylc9H62sw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-05-30T20:44:24.0417837+00:00"}, "ghJBwhT0SSCYJQJpZ5Cq8PFy/XGiVJnpwLoDI9fNP68=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-05-30T20:44:24.0483099+00:00"}, "3YdXFCmKMMCuKOdrEdo4ptvnbY4/73dyOJG9vNxqgEw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-05-30T20:44:24.0811883+00:00"}, "JKLRzsTBSUz+bUy0D+RUeTEq9LbpsmWhObvvAkGldhU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-05-30T20:44:24.0932359+00:00"}, "ssxji61bbfNV/53PBec4TXQnU7433onqix8U4rA7/sY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-05-30T20:44:24.1122726+00:00"}, "id2HW3Vkb2+2SPeTcDTZaMlb4PatAJcfyhmkOIhWBOA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-05-30T20:44:24.1167807+00:00"}, "C9z2N0d0U422ixAMGAC+e7CLW4TpryBXTBOuBqwShpM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8ngr7uwcar-eh7blpvql2.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/admin#[.{fingerprint=eh7blpvql2}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\admin.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y6crq8vke3", "Integrity": "hh/jNxQ0vB3m5P1qYL3fbcZzfPC6/hPW2fPgnm2i3AM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\admin.css", "FileLength": 6376, "LastWriteTime": "2025-05-30T20:44:24.1254581+00:00"}, "KPlsNIqCe9Ya6xSZNQzYHU7yEjrnz5Ewvj2zAXrApZI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pn3zjfqylx-g07bnx142d.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/client#[.{fingerprint=g07bnx142d}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\client.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u372l94rk3", "Integrity": "hnZ3ayaGZFLAG6FjKMU6gjkmJLNka/tR9IAAQRpSPoc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\client.css", "FileLength": 4346, "LastWriteTime": "2025-05-30T20:44:24.1264574+00:00"}, "AcbVExNb/smbauQhZtYgNylIpSKpwpdOPKZoonuOW1U=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\elvx5emtjm-wilnnc3w1m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/custom#[.{fingerprint=wilnnc3w1m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\custom.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k58f1us78f", "Integrity": "CDABZ8Got8gWH2Wic8T6MC1izVu156P7c25rWmLiqLI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\custom.css", "FileLength": 212, "LastWriteTime": "2025-05-30T20:44:24.1284581+00:00"}, "G1RYOw9hYYj7XSf8AUxDFMVaIScAl9BRmRAOyOr0c5k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ypbfssc6px-1edbnb0gu6.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/hero-slideshow#[.{fingerprint=1edbnb0gu6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\hero-slideshow.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kg1721j3l3", "Integrity": "3CYDHawx/sbpzjIQoN7+wnjWSSXtjNUj7txCQ3kydJI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\hero-slideshow.css", "FileLength": 1718, "LastWriteTime": "2025-05-30T20:44:24.1304562+00:00"}, "dFiD1Amb9kz+sZ2O7oQpwa+WG0dE47RxtzRrdQ5PX6g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\xtt04yhxc6-z0oxv65qqt.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/modern-homepage#[.{fingerprint=z0oxv65qqt}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\modern-homepage.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8f892lsdcx", "Integrity": "HLcn/vkafXzbY1fT/tep63CZkPI+ruuC0QqtN3b9d/g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\modern-homepage.css", "FileLength": 14915, "LastWriteTime": "2025-05-30T20:44:24.1334546+00:00"}, "0rQ8LnpKh5gy9InAJwK1olgTaHMMUId4c147DT/Au18=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7ejlrje5jg-8znpj9knio.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/site#[.{fingerprint=8znpj9knio}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tb45argtbf", "Integrity": "KXsoPJcXUP9jdH3J4w7jvn/bvOC4Ia8KvN2KNFAyres=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\site.css", "FileLength": 2281, "LastWriteTime": "2025-05-30T20:44:24.1339584+00:00"}, "ZnUt2RD4VIBHq+NHosyq9ua+C8mVwhy3KoFn8ZL4KmQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\03l1rte6gq-61n19gt1b8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-05-30T20:44:24.0122579+00:00"}, "0XChipx8UadACIlcJBixqp+Pw6GrtZn6RDoSULc6Uxc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ktm00ucnju-wgrwvlfr5s.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/delphi#[.{fingerprint=wgrwvlfr5s}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\delphi.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkbigiuy64", "Integrity": "HQM1ysqWHmUnuAlp3aK0g+H3KTdRrGOY3H0Vq22/RWA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\delphi.svg", "FileLength": 1935, "LastWriteTime": "2025-05-30T20:44:24.0207621+00:00"}, "u3xkZKC+ptZrvuHunL55s+4ETiHCMWSWXNZ/T1clPhA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\qveyjng0ys-nedvdbx254.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/haskell#[.{fingerprint=nedvdbx254}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\haskell.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "edsoh1itdo", "Integrity": "olpJlaPKyLgPjxsEUdPEwIwMpvYmE5KFaI68j+OJmJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\haskell.svg", "FileLength": 336, "LastWriteTime": "2025-05-30T20:44:24.0232656+00:00"}, "5UZ+aNPuIknZZCGiQeiqjVMxYyw/eRc9uIOu2qnOulg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ikbcgss9f7-k8cq9l2kib.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/nodejs#[.{fingerprint=k8cq9l2kib}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\nodejs.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lpoc1bcrbp", "Integrity": "GTXsRzccKflDEkzr6cT5Iq+5KyyN46VgzOgbjaIisWo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\nodejs.svg", "FileLength": 721, "LastWriteTime": "2025-05-30T20:44:24.0262815+00:00"}, "UeTXOcdNMBD6rduEVfDUMd7F4h6LRC0BT8+CZR7Tl0U=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nnketcjwya-1x8wi64s97.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/python#[.{fingerprint=1x8wi64s97}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\python.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j6yyh4b7nm", "Integrity": "i5K/+SeOT5dWe0FWBa0GIm1JLWXKxGhZVNxsYkLDrns=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\python.svg", "FileLength": 734, "LastWriteTime": "2025-05-30T20:44:24.0292805+00:00"}, "Oul48G6GLVEfuzbOan+gZeSN8lXx9XVp9k506rVdYAg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uham8ign5j-emsbkume29.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/swift#[.{fingerprint=emsbkume29}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\swift.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvaavaxrke", "Integrity": "afO1CiL+LYXrmSV0hG/nzRY58iy/trDc+maeMmc1q8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\swift.svg", "FileLength": 619, "LastWriteTime": "2025-05-30T20:44:24.03028+00:00"}, "vE+MEf5yLKgtXCyzuV/8GEsQMD69Ey8HhszyjN38lS8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\10owx4zk9i-y36mr8d3re.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technoloway-logo#[.{fingerprint=y36mr8d3re}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technoloway-logo.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "78ry7ld12l", "Integrity": "qVZsciNX4bvJyG/NFAFG6/1bWRdPLgFZLpfMSEqLyZo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technoloway-logo.svg", "FileLength": 110221, "LastWriteTime": "2025-05-30T20:44:24.0367867+00:00"}, "xQVYSg4ELkzmSyBlT8SYQk7PdbojyfOJnLv79qt7BwQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\02bln8vcid-jl681mf0b7.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/admin#[.{fingerprint=jl681mf0b7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\admin.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8if5f0377p", "Integrity": "QMN/wAbHwqbRNzWuejiHrLRhyP1n9oF4cqNsHGtp2AU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\admin.js", "FileLength": 4396, "LastWriteTime": "2025-05-30T20:44:24.0387855+00:00"}, "d7v0ap/A4XzjPZZ5W98jCTEAUo/8XB98ybu5e/wFL5o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v8u4sxh3fr-3xu3gfddrx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/client#[.{fingerprint=3xu3gfddrx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\client.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kkcwqz3pwi", "Integrity": "pD2v2NXmu8dIQdlAvDxSQC28LtmTLNKzC22UlumYprU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\client.js", "FileLength": 390, "LastWriteTime": "2025-05-30T20:44:24.0397849+00:00"}, "hsPgZdurPdJD4Fw7rpdjy88sBcuRTujwqjXntFn8CtY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\h4d7zuu3lf-rd81zyuzir.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/hero-slideshow#[.{fingerprint=rd81zyuzir}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\hero-slideshow.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6lq72lcfcg", "Integrity": "pkfBPs6ownzjPebnXD0fcxTJYLLrd3SJgTGnCXXCxXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\hero-slideshow.js", "FileLength": 1678, "LastWriteTime": "2025-05-30T20:44:24.0417837+00:00"}, "D+JfMlzPz3/IUTRvZUjH+m1ZMPWkpe0c77Iofj7RRBY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\br1wthuj05-wi7ss39h6q.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/homepage-animations#[.{fingerprint=wi7ss39h6q}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\homepage-animations.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iftq384gbj", "Integrity": "gqWjPa9AQ/+yPWfZCbXLhtYUgjFs8Oxn+qn1YXdxUNA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\homepage-animations.js", "FileLength": 2761, "LastWriteTime": "2025-05-30T20:44:24.0417837+00:00"}, "uy1wAd3wdcLuQcOPd27Wx6FjUMeQYJJC5CTkFi4E0AU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\5eaaj8jli5-2svxw61t86.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/site#[.{fingerprint=2svxw61t86}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o75gd3adie", "Integrity": "ZHcCB5qku88L0iD5sq5euDqrhPm9N+4MeMk/tRrrgIg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\site.js", "FileLength": 769, "LastWriteTime": "2025-05-30T20:44:24.044309+00:00"}, "eyey+rWOUBo+clZdAQf/hNo7ZRPWnWKnDsekLLtXYZc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\huncoxf4nc-bqjiyaj88i.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-05-30T20:44:24.0493094+00:00"}, "aXsy2txNQSn6KcY+6cbgTOZrCLqnY70lLXVXjXn54u4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2jnr46nmpc-c2jlpeoesf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-05-30T20:44:24.0716641+00:00"}, "/MlwSSm4q3dvQldOF9x2ZuNPefuqm9B+TRZj9OYhnMI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wmzyo7xuhb-erw9l3u2r3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-05-30T20:44:24.0741889+00:00"}, "emCiqI5R90cgCECc1G1wBLQEygjN8d0OemP4UcumHDs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\djyxqsu5pv-aexeepp0ev.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-05-30T20:44:24.0761908+00:00"}, "AhUyLq93sV+Fo5JPOaWja+Pnv6hI8G99+DaFW5VwSVU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2sbuxwf498-d7shbmvgxk.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-05-30T20:44:24.0781906+00:00"}, "LdjbR07qhx3ZPyhFm/dSVjMG4lfhdUQov8yJIP80428=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\fe676pkbci-ausgxo2sd3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-05-30T20:44:24.0197628+00:00"}, "58MTlyRb8DOzDGE0LibE9kuQTigTlXOEx0aeeECUSWA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2n44t9y1oe-k8d9w2qqmf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-05-30T20:44:24.0601587+00:00"}, "L/Ic/8HSOkLkcKR5RvzrtCnq5XPv3MlXKQ0hpCCovHE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2yiqndbjob-cosvhxvwiu.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-05-30T20:44:24.0716641+00:00"}, "iCl99/bSN1ZmEVRrUEbGrGYEOuSqG+4PJs9I28Vqj/8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\6laymexpp1-ub07r2b239.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-05-30T20:44:24.0731689+00:00"}, "OjdJPhfvlRFtsy4ZC7mKbzOjB/9ayjOcqcfs1MtTRmY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e23n28kbl6-fvhpjtyr6v.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-05-30T20:44:24.0856954+00:00"}, "CTanv8wv6beze300ElYquZ8sbFkCrC0SFpWjJpGNaiI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\0xdmrg7j3x-b7pk76d08c.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-05-30T20:44:24.0866953+00:00"}, "rpNcsYNLXiQNZ8l3q9bdWKgwpVXtVsJTYgr2FgcUsLY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7msow90u6m-fsbi9cje9m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-05-30T20:44:24.0922364+00:00"}, "yUhTEoGZZ0t6faRe+6mrnN6wGV/RPt/ynIqmdVunFjA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\y6lzeo23io-rzd6atqjts.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-05-30T20:44:24.0821871+00:00"}, "8ezbZG/H00TAmaxJY28fB6/24DAxoTwLH5gSdumj/Pk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\b1eq4g63qz-ee0r1s7dh0.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-05-30T20:44:24.0856954+00:00"}, "ixeoNkBG2adlhb87Ur23zCXsvTDwEWw167M19Ej0m1g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\q7xywl88yg-dxx9fxp4il.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-05-30T20:44:24.0866953+00:00"}, "C+u2n1XqrI8E5btJ5U3FTyR3FmP2GH78SAU5Qokz4cs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tl4yfr4qca-jd9uben2k1.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-05-30T20:44:24.0876945+00:00"}, "EJPWZ0zpVLphZKx1ZSDUvOWpCNgos35JoIdWuqUFJOU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8xb1bhl2rn-khv3u5hwcm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-05-30T20:44:24.0902381+00:00"}, "1M/cU5sM9EAffnF8IUvygpZl5XjKLqegYr5F6TN6x3s=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\1ju4p3k34w-r4e9w2rdcm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-05-30T20:44:24.0967459+00:00"}, "n3mbxxCciZ+m4iUEvJ1JLhB+c+cihwlCbkzidMcpVt4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\jyl4075mxi-lcd1t2u6c8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-05-30T20:44:24.0987436+00:00"}, "2EuG6ndONlu4fz6kHMFnpqjWB8VMCMVOw0zorsnVcBI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zul1z3i66m-c2oey78nd0.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-05-30T20:44:24.1017411+00:00"}, "7GAuDHeZ3E8SKhuSMKAFwdbxqQAy7O1F1xWgVbiYpaE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tj8j7uiu1e-tdbxkamptv.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-05-30T20:44:24.1062756+00:00"}, "ed//+NIgnZIKHtwD7PjLkNxNaA30+XbTFZ34+M+YnYw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\l6ou9js1o2-j5mq2jizvt.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-05-30T20:44:24.1177802+00:00"}, "aETklFEzUMwkPlk/8CM8HGrDS5F1OQuBya8djrKL0Nw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\z3s3pqtpk8-06098lyss8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-05-30T20:44:24.0217612+00:00"}, "pUsrFCdsnzo28X3aqTnDt4Q4oASFv8oz1HEmoZHO+sg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pkmk90gfad-nvvlpmu67g.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-05-30T20:44:24.0252794+00:00"}, "Ll0nh2xzSt7NX2l674c7qQ+j6GUrEfACQn2uJb/KtCM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zktux6y5y4-s35ty4nyc5.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-05-30T20:44:24.0312796+00:00"}, "++0frOA6abau1S9ho/VE71nyqK8EwXu5q/W5nepw7A4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\26o4et715b-pj5nd1wqec.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-05-30T20:44:24.0432876+00:00"}, "OzZ3B65jXBFx1RQ3oiB6GJ/3itALuvo1N0OsECDBwGc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7gp52xdync-46ein0sx1k.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-05-30T20:44:24.0483099+00:00"}, "gpkfSyet5AXjZa/AeWp+0KHBZlTLD0KRFMC4jbcY5Jo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e7b6u3x55w-v0zj4ognzu.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-05-30T20:44:24.0937392+00:00"}, "ZkiDmDbSctbKuZlxd2OJl/6eLKw0jTwAamBySt8cgw4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9zg9c6chb7-37tfw0ft22.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-05-30T20:44:24.1082745+00:00"}, "fmxNI7i8nHRMKhRbWfRCuUK8Kl8CzydyrB+ctAiBV/w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\33czbugvzg-hrwsygsryq.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-05-30T20:44:24.1409615+00:00"}, "aIQjxER330W+3vt1jJ3MqnPoR//UGEOgdQKgvc/nUYU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2d5epgijjo-pk9g2wxc8p.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-05-30T20:44:24.1504885+00:00"}, "Rjh8IVfIc1hkdM6XRY500yhX6iM2O/0Ouej9OQ+dqhU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t63suxyd54-ft3s53vfgj.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-05-30T20:44:24.1708042+00:00"}, "nd2KNy8bYqgPXXVaZmPTrnrBdOFreCksnwYJ5cCrXQM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\w5r96tufml-6cfz1n2cew.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-05-30T20:44:24.1753313+00:00"}, "sNIYYfPqWnq2CKsd86Lf7W/MlwtwJFY/ek/3xdJYqOU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wegvxza2ge-6pdc2jztkx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-05-30T20:44:24.1893332+00:00"}, "CuAt8KIFzYosTBIsLSjN4YaaZCholwWLAi74SaX14qc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g09mkto0sq-493y06b0oq.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-05-30T20:44:24.1918958+00:00"}, "7dxJO+H6AmZAm+esnY62ccAjiiVb8ssAhfjq9fIOIVo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\4mg52w1lo3-iovd86k7lj.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-05-30T20:44:24.199678+00:00"}, "zWHW9KyLscOIUizvRyLQANRjwHPXk8Ee6tY7X2yX9iE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\lh2isbcsad-vr1egmr9el.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-05-30T20:44:24.2035274+00:00"}, "Z25C1W0MHTRbhBkog7sUAhB+FNG+sshAZ9k4aPGFhG0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g1ftby5py3-kbrnm935zg.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-05-30T20:44:24.1589934+00:00"}, "BTIk0QfoqP0ooUR6a1sK5OEtXky4mBPkeyn5rRsEsOc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vphg83v0d2-jj8uyg4cgr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-05-30T20:44:24.1645192+00:00"}, "EEYxZB8+v4TUIJDkiIWy7TDudjbxcyIJ/hz63RvrOpQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\hd7xpsd4po-y7v9cxd14o.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-05-30T20:44:24.0217612+00:00"}, "p+Y6jgHcTUZs1uBkxac9XWRs0UF/n4Bb/k5icG+8Dm0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2626qsfhg1-notf2xhcfb.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-05-30T20:44:24.0377874+00:00"}, "TVSe36T9valgrug6dBrQT5cV4PLcs/SR+CitqaxWyQY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\u8c68735sf-h1s4sie4z3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-05-30T20:44:24.0761908+00:00"}, "KfEWNPyZteelOAn2wjk5auzpIoVkXTP0Il+O8tO7GjU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\fij7bwzp8x-63fj8s7r0e.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-05-30T20:44:24.0876945+00:00"}, "ibdB2cua4hzYTJ0HWZVkEx+8DkS8BkoNBRdjpgYVl14=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\cji4aywtqv-0j3bgjxly4.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-05-30T20:44:24.1072752+00:00"}, "ctBphz1nfw4C/ouDWFv0rqNsSBXszLlek5aVYzuWd9Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9mvculopuj-47otxtyo56.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-05-30T20:44:24.1092741+00:00"}, "tPyrYlFNHH8CySkrns7Llzo60utfeZlkFqNlQIv3OVs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bf5okwooxo-4v8eqarkd7.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-05-30T20:44:24.1102725+00:00"}, "jr1rJSndtlwB56ux1BIIm3JjVXFdRQijCuwW83O7wqo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g10371glbs-356vix0kms.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-05-30T20:44:24.1157813+00:00"}, "DnVYv1LpvkJLmXa66wplZuhNN0R4ko5CChJsNR9ZvJw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2iu5gkm6iv-83jwlth58m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-05-30T20:44:24.1254581+00:00"}, "+jsVCGlnH7Udm16MNBB0eST8zjzMqAMV2uUvzAgd5ig=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ahn74umtfx-mrlpezrjn3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-05-30T20:44:24.1264574+00:00"}, "bCA/DSL0k2wm83sDuJPr/J3JetCMiDWpctSDZsfrGxw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8k9meis7w4-lzl9nlhx6b.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-05-30T20:44:24.1294557+00:00"}, "AwAZePXAO2fJFab5N9qERtsL4Q+UELiQhYDh8Pe8Fxw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\6jty019ari-ag7o75518u.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-05-30T20:44:24.1314555+00:00"}, "NpmfVo0EmxbshmXkqNWaI6hbnFr/TIewoDe3oC+96QM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\w5qk1pgd3r-x0q3zqp4vz.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-05-30T20:44:24.1339584+00:00"}, "j1AFaoo2JK+pGx6rKnRBjLlvrPT1Zi+arDB/W+iR6h4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\64rjhqzavh-0i3buxo5is.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-05-30T20:44:24.1434654+00:00"}, "2skbOPUmBI/1U9LowRyS9X+zTejn6kri5Qryzxv2kzw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\dmzx9f6yhi-o1o13a6vjx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-05-30T20:44:24.1464909+00:00"}, "oTYwfkkV4AuVaM2g4xlCDtPkC+0201RlS0nZhYD3/E0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zydbwp8g7y-ttgo8qnofa.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-05-30T20:44:24.1484894+00:00"}, "/q3tPuPqYiytrXLuoHPGJybOiyrMUbvl7LLsd7DllCw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tivk7iw1cd-2z0ns9nrw6.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-05-30T20:44:24.1609924+00:00"}, "xxVUJfH2iCJZerbirUS8qJKi8wf/TNxkOgNy1k+8180=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2v4zxome5j-muycvpuwrr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-05-30T20:44:24.0137604+00:00"}, "jMXgPt3KgbnFQRPbxr64lg6bfGIvSgSt2QDWEiJrNG0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e15jphxp1u-87fc7y1x7t.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-05-30T20:44:24.0387855+00:00"}, "PxiZP0KJcU8GPlwIEWvI95I3/O2lopCpQRYKDBgT1WI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8ezk5r3swy-mlv21k5csn.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-05-30T20:44:24.0417837+00:00"}, "q3bBZ3nYFhbHCO/geC+WsAPqNEf00U0rmSewcA1Uk9Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ot0u7hstz0-btisj24con.gz", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web#[.{fingerprint=btisj24con}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Technoloway.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0ib5o3ag5", "Integrity": "72RHN3eTubIZVjEZactNL1gtgMjQIrVfDuIfhwQDTvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Technoloway.Web.styles.css", "FileLength": 545, "LastWriteTime": "2025-05-30T20:44:24.0432876+00:00"}, "DfWCPBYC9bRgowNnzowBilNGoGlquuXG4uVJqLeXhfM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\on9a315cdm-btisj24con.gz", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web#[.{fingerprint=btisj24con}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Technoloway.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0ib5o3ag5", "Integrity": "72RHN3eTubIZVjEZactNL1gtgMjQIrVfDuIfhwQDTvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Technoloway.Web.bundle.scp.css", "FileLength": 545, "LastWriteTime": "2025-05-30T20:44:24.0473104+00:00"}}, "CachedCopyCandidates": {}}