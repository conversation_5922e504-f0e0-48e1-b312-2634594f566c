using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;
using System.Linq;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class HomeController : Controller
{
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<Core.Entities.Client> _clientRepository;
    private readonly IRepository<Invoice> _invoiceRepository;
    private readonly IRepository<ContactForm> _contactFormRepository;

    public HomeController(
        IRepository<Project> projectRepository,
        IRepository<Core.Entities.Client> clientRepository,
        IRepository<Invoice> invoiceRepository,
        IRepository<ContactForm> contactFormRepository)
    {
        _projectRepository = projectRepository;
        _clientRepository = clientRepository;
        _invoiceRepository = invoiceRepository;
        _contactFormRepository = contactFormRepository;
    }

    public async Task<IActionResult> Index()
    {
        var projectCount = await _projectRepository.CountAsync();
        var clientCount = await _clientRepository.CountAsync();
        var invoiceCount = await _invoiceRepository.CountAsync();
        var pendingInvoiceCount = await _invoiceRepository.CountAsync(i => i.Status == "Pending");
        var unreadContactCount = await _contactFormRepository.CountAsync(c => !c.IsRead);

        ViewBag.ProjectCount = projectCount;
        ViewBag.ClientCount = clientCount;
        ViewBag.InvoiceCount = invoiceCount;
        ViewBag.PendingInvoiceCount = pendingInvoiceCount;
        ViewBag.UnreadContactCount = unreadContactCount;

        var recentProjects = await _projectRepository.ListAsync(p => !p.IsDeleted);
        var recentInvoices = await _invoiceRepository.ListAsync(i => !i.IsDeleted);

        ViewBag.RecentProjects = recentProjects.AsEnumerable().OrderByDescending(p => p.CreatedAt).Take(5).ToList();
        ViewBag.RecentInvoices = recentInvoices.AsEnumerable().OrderByDescending(i => i.CreatedAt).Take(5).ToList();

        return View();
    }
}
