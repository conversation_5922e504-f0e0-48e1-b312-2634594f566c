// Enhanced Admin Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar functionality
    initializeSidebar();

    // Initialize navigation
    initializeNavigation();

    // Initialize animations
    initializeAnimations();

    // Initialize theme
    initializeTheme();
});

// Sidebar functionality
function initializeSidebar() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const wrapper = document.querySelector('#wrapper');
    const sidebar = document.querySelector('.modern-sidebar');

    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            wrapper.classList.toggle('toggled');

            // Add animation class
            sidebar.classList.add('sidebar-animating');
            setTimeout(() => {
                sidebar.classList.remove('sidebar-animating');
            }, 300);
        });
    }

    // Close sidebar on outside click (mobile)
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 768) {
            const isClickInsideSidebar = sidebar.contains(e.target);
            const isToggleButton = sidebarToggle.contains(e.target);

            if (!isClickInsideSidebar && !isToggleButton && !wrapper.classList.contains('toggled')) {
                wrapper.classList.add('toggled');
            }
        }
    });
}

// Enhanced navigation functionality
function initializeNavigation() {
    const currentPath = window.location.pathname.toLowerCase();
    const navItems = document.querySelectorAll('.nav-item');

    // Remove active class from all items
    navItems.forEach(item => item.classList.remove('active'));

    // Add active class to current page
    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href) {
            const hrefPath = href.toLowerCase();

            // Check for exact match or if current path contains the nav path
            if (currentPath === hrefPath ||
                (hrefPath !== '/admin' && currentPath.includes(hrefPath)) ||
                (currentPath.includes('/admin/home') && hrefPath.includes('/admin/home'))) {
                item.classList.add('active');

                // Add pulse animation to active item
                const icon = item.querySelector('.nav-icon');
                if (icon) {
                    icon.style.animation = 'pulse 2s infinite';
                }
            }
        }
    });

    // Add hover effects
    navItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(8px)';

            const icon = this.querySelector('.nav-icon');
            if (icon && !this.classList.contains('active')) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
        });

        item.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateX(0)';
            } else {
                this.style.transform = 'translateX(4px)';
            }

            const icon = this.querySelector('.nav-icon');
            if (icon && !this.classList.contains('active')) {
                icon.style.transform = 'scale(1)';
            }
        });
    });
}

// Animation enhancements
function initializeAnimations() {
    // Stagger animation for nav items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach((item, index) => {
        item.style.animationDelay = `${(index + 1) * 0.1}s`;
    });

    // Brand logo animation
    const brandIcon = document.querySelector('.brand-icon');
    if (brandIcon) {
        brandIcon.addEventListener('mouseenter', function() {
            this.style.transform = 'rotate(5deg) scale(1.1)';
        });

        brandIcon.addEventListener('mouseleave', function() {
            this.style.transform = 'rotate(0deg) scale(1)';
        });
    }

    // User avatar animation
    const userAvatar = document.querySelector('.user-avatar');
    if (userAvatar) {
        userAvatar.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(5deg)';
        });

        userAvatar.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    }

    // Sidebar user hover effect
    const sidebarUser = document.querySelector('.sidebar-user');
    if (sidebarUser) {
        sidebarUser.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.15)';
        });

        sidebarUser.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    }
}

// Theme and visual enhancements
function initializeTheme() {
    // Add smooth scrolling to sidebar
    const sidebar = document.querySelector('.modern-sidebar');
    if (sidebar) {
        sidebar.style.scrollBehavior = 'smooth';
    }

    // Add loading animation
    document.body.classList.add('admin-loaded');

    // Enhance nav section titles
    const sectionTitles = document.querySelectorAll('.nav-section-title');
    sectionTitles.forEach(title => {
        title.addEventListener('mouseenter', function() {
            this.style.color = 'rgba(255, 255, 255, 0.8)';
        });

        title.addEventListener('mouseleave', function() {
            this.style.color = 'rgba(255, 255, 255, 0.5)';
        });
    });
}

// Utility functions
function addRippleEffect(element, event) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');

    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Add ripple effect to nav items
document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            addRippleEffect(this, e);
        });
    });
});

// Responsive handling
window.addEventListener('resize', function() {
    const wrapper = document.querySelector('#wrapper');

    if (window.innerWidth >= 768) {
        wrapper.classList.remove('toggled');
    } else {
        wrapper.classList.add('toggled');
    }
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .nav-item {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    body.admin-loaded .modern-sidebar {
        animation: slideInLeft 0.5s ease-out;
    }

    .sidebar-animating {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
`;
document.head.appendChild(style);
