@{
    ViewData["Title"] = "Admin Dashboard";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Admin Dashboard</h1>
            <p class="text-muted mb-0">Welcome back! Here's what's happening with your business today.</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary">
                <i class="fas fa-download"></i>
                Export Report
            </button>
            <button class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Quick Add
            </button>
        </div>
    </div>

    <!-- Modern Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Projects</p>
                        <h3 class="admin-stat-number">@ViewBag.ProjectCount</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Active Clients</p>
                        <h3 class="admin-stat-number">@ViewBag.ClientCount</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Invoices</p>
                        <h3 class="admin-stat-number">@ViewBag.InvoiceCount</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Pending Invoices</p>
                        <h3 class="admin-stat-number">@ViewBag.PendingInvoiceCount</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row g-4">
        <!-- Recent Projects -->
        <div class="col-lg-6">
            <div class="admin-card h-100">
                <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
                    <div>
                        <h5 class="mb-0 fw-bold text-gray-800">Recent Projects</h5>
                        <p class="text-muted mb-0 small">Latest project activities</p>
                    </div>
                    <a asp-area="Admin" asp-controller="Projects" asp-action="Index" class="btn-modern-admin primary">
                        <i class="fas fa-arrow-right"></i>
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    @if (ViewBag.RecentProjects != null && ViewBag.RecentProjects.Count > 0)
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th class="border-0">Project</th>
                                        <th class="border-0">Client</th>
                                        <th class="border-0">Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var project in ViewBag.RecentProjects)
                                    {
                                        <tr>
                                            <td class="border-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="admin-stat-icon me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                                        <i class="fas fa-project-diagram"></i>
                                                    </div>
                                                    <div>
                                                        <a asp-area="Admin" asp-controller="Projects" asp-action="Details" asp-route-id="@project.Id"
                                                           class="fw-semibold text-decoration-none text-gray-800">
                                                            @project.Name
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="border-0">
                                                <span class="text-muted">@project.ClientName</span>
                                            </td>
                                            <td class="border-0">
                                                <span class="badge bg-light text-dark">@project.CreatedAt.ToString("MMM dd")</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-project-diagram text-muted mb-3" style="font-size: 3rem;"></i>
                            <p class="text-muted">No projects found.</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Recent Invoices -->
        <div class="col-lg-6">
            <div class="admin-card h-100">
                <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
                    <div>
                        <h5 class="mb-0 fw-bold text-gray-800">Recent Invoices</h5>
                        <p class="text-muted mb-0 small">Latest billing activities</p>
                    </div>
                    <a asp-area="Admin" asp-controller="Invoices" asp-action="Index" class="btn-modern-admin primary">
                        <i class="fas fa-arrow-right"></i>
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    @if (ViewBag.RecentInvoices != null && ViewBag.RecentInvoices.Count > 0)
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th class="border-0">Invoice</th>
                                        <th class="border-0">Amount</th>
                                        <th class="border-0">Status</th>
                                        <th class="border-0">Due Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var invoice in ViewBag.RecentInvoices)
                                    {
                                        <tr>
                                            <td class="border-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="admin-stat-icon info me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                                        <i class="fas fa-file-invoice"></i>
                                                    </div>
                                                    <div>
                                                        <a asp-area="Admin" asp-controller="Invoices" asp-action="Details" asp-route-id="@invoice.Id"
                                                           class="fw-semibold text-decoration-none text-gray-800">
                                                            @invoice.InvoiceNumber
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="border-0">
                                                <span class="fw-semibold">$@invoice.TotalAmount.ToString("N2")</span>
                                            </td>
                                            <td class="border-0">
                                                @if (invoice.Status == "Paid")
                                                {
                                                    <span class="badge bg-success">Paid</span>
                                                }
                                                else if (invoice.Status == "Pending")
                                                {
                                                    <span class="badge bg-warning">Pending</span>
                                                }
                                                else if (invoice.Status == "Overdue")
                                                {
                                                    <span class="badge bg-danger">Overdue</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">@invoice.Status</span>
                                                }
                                            </td>
                                            <td class="border-0">
                                                <span class="text-muted">@invoice.DueDate.ToString("MMM dd")</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice-dollar text-muted mb-3" style="font-size: 3rem;"></i>
                            <p class="text-muted">No invoices found.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
